services:
  backend:
    image: ghcr.io/suna-ai/suna-backend:latest
    platform: linux/amd64
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend/.env:/app/.env:ro
    env_file:
      - ./backend/.env
    environment:
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_SSL=${REDIS_SSL:-False}
      - RABBITMQ_HOST=${RABBITMQ_HOST:-rabbitmq}
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-guest}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-guest}
      - RABBITMQ_VHOST=${RABBITMQ_VHOST:-/}
    depends_on:
      worker:
        condition: service_started
    deploy:
      resources:
        limits:
          memory: 12G
          cpus: "6"

  worker:
    image: ghcr.io/suna-ai/suna-backend:latest
    platform: linux/amd64
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: uv run dramatiq --skip-logging --processes 8 --threads 8 run_agent_background
    volumes:
      - ./backend/.env:/app/.env:ro
    env_file:
      - ./backend/.env
    environment:
      - REDIS_HOST=${REDIS_HOST:-redis}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_SSL=${REDIS_SSL:-False}
      - RABBITMQ_HOST=${RABBITMQ_HOST:-rabbitmq}
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-guest}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-guest}
      - RABBITMQ_VHOST=${RABBITMQ_VHOST:-/}
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: "2"

  frontend:
    init: true
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend/.env.local:/app/.env.local:ro
    environment:
      - NODE_ENV=production
    command: ["npm", "run", "start"]
    depends_on:
      - backend
